// Copyright 2021 Datafuse Labs
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use std::cmp::Ordering;
use std::marker::PhantomData;

use super::AccessType;
use crate::Column;
use crate::Domain;
use crate::ScalarRef;

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq)]
pub struct UnaryType<T>(PhantomData<T>);

impl<T> AccessType for UnaryType<T>
where T: AccessType
{
    type Scalar = T::Scalar;
    type ScalarRef<'a> = T::ScalarRef<'a>;
    type Column = T::Column;
    type Domain = T::Domain;
    type ColumnIterator<'a> = T::ColumnIterator<'a>;

    fn to_owned_scalar(scalar: Self::ScalarRef<'_>) -> Self::Scalar {
        T::to_owned_scalar(scalar)
    }

    fn to_scalar_ref(scalar: &Self::Scalar) -> Self::ScalarRef<'_> {
        T::to_scalar_ref(scalar)
    }

    fn try_downcast_scalar<'a>(scalar: &ScalarRef<'a>) -> Option<Self::ScalarRef<'a>> {
        T::try_downcast_scalar(scalar)
    }

    fn try_downcast_domain(domain: &Domain) -> Option<Self::Domain> {
        T::try_downcast_domain(domain)
    }

    fn try_downcast_column(col: &Column) -> Option<Self::Column> {
        T::try_downcast_column(col)
    }

    fn column_len(col: &Self::Column) -> usize {
        T::column_len(col)
    }

    fn index_column(col: &Self::Column, index: usize) -> Option<Self::ScalarRef<'_>> {
        T::index_column(col, index)
    }

    unsafe fn index_column_unchecked(col: &Self::Column, index: usize) -> Self::ScalarRef<'_> {
        T::index_column_unchecked(col, index)
    }

    fn slice_column(col: &Self::Column, range: std::ops::Range<usize>) -> Self::Column {
        T::slice_column(col, range)
    }

    fn iter_column(col: &Self::Column) -> Self::ColumnIterator<'_> {
        T::iter_column(col)
    }

    fn compare(lhs: Self::ScalarRef<'_>, rhs: Self::ScalarRef<'_>) -> Ordering {
        T::compare(lhs, rhs)
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct PairType<A, B>(PhantomData<(A, B)>);

impl<A, B> AccessType for PairType<A, B>
where
    A: AccessType,
    B: AccessType,
{
    type Scalar = (A::Scalar, B::Scalar);
    type ScalarRef<'a> = (A::ScalarRef<'a>, B::ScalarRef<'a>);
    type Column = (A::Column, B::Column);
    type Domain = (A::Domain, B::Domain);
    type ColumnIterator<'a> = std::iter::Zip<A::ColumnIterator<'a>, B::ColumnIterator<'a>>;

    fn to_owned_scalar((a, b): Self::ScalarRef<'_>) -> Self::Scalar {
        (A::to_owned_scalar(a), B::to_owned_scalar(b))
    }

    fn to_scalar_ref((a, b): &Self::Scalar) -> Self::ScalarRef<'_> {
        (A::to_scalar_ref(a), B::to_scalar_ref(b))
    }

    fn try_downcast_scalar<'a>(scalar: &ScalarRef<'a>) -> Option<Self::ScalarRef<'a>> {
        let [a, b] = scalar.as_tuple()?.as_slice() else {
            return None;
        };
        Some((A::try_downcast_scalar(a)?, B::try_downcast_scalar(b)?))
    }

    fn try_downcast_domain(domain: &Domain) -> Option<Self::Domain> {
        let [a, b] = domain.as_tuple()?.as_slice() else {
            return None;
        };
        Some((A::try_downcast_domain(a)?, B::try_downcast_domain(b)?))
    }

    fn try_downcast_column(col: &Column) -> Option<Self::Column> {
        let [a, b] = col.as_tuple()?.as_slice() else {
            return None;
        };
        Some((A::try_downcast_column(a)?, B::try_downcast_column(b)?))
    }

    fn column_len((a, _b): &Self::Column) -> usize {
        debug_assert_eq!(A::column_len(a), B::column_len(_b));
        A::column_len(a)
    }

    fn index_column((a, b): &Self::Column, index: usize) -> Option<Self::ScalarRef<'_>> {
        Some((A::index_column(a, index)?, B::index_column(b, index)?))
    }

    unsafe fn index_column_unchecked((a, b): &Self::Column, index: usize) -> Self::ScalarRef<'_> {
        (
            A::index_column_unchecked(a, index),
            B::index_column_unchecked(b, index),
        )
    }

    fn slice_column((a, b): &Self::Column, range: std::ops::Range<usize>) -> Self::Column {
        (A::slice_column(a, range.clone()), B::slice_column(b, range))
    }

    fn iter_column((a, b): &Self::Column) -> Self::ColumnIterator<'_> {
        A::iter_column(a).zip(B::iter_column(b))
    }

    fn compare(
        (lhs_a, lhs_b): Self::ScalarRef<'_>,
        (rhs_a, rhs_b): Self::ScalarRef<'_>,
    ) -> Ordering {
        match A::compare(lhs_a, rhs_a) {
            std::cmp::Ordering::Equal => B::compare(lhs_b, rhs_b),
            ord => ord,
        }
    }
}

// Ternary
